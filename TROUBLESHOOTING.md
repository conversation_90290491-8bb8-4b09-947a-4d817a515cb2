# دليل حل المشاكل - إضافة المراجعات المخصصة

## 🚨 المشاكل الشائعة والحلول

### 1. خطأ "Class CustomReview_Database not found"

**السبب:** اسم مجلد الإضافة يحتوي على مسافات أو الملفات لم يتم رفعها بشكل صحيح.

**الحل:**
```bash
# تأكد من أن اسم المجلد هو:
custom-review-plugin

# وليس:
"custom review" أو "custom-review plugin"
```

**خطوات الإصلاح:**
1. إلغاء تفعيل الإضافة
2. إعادة تسمية المجلد إلى `custom-review-plugin`
3. إعادة تفعيل الإضافة

### 2. الإضافة لا تظهر في قائمة الإضافات

**الأسباب المحتملة:**
- مجلد الإضافة في مكان خاطئ
- ملف `custom-review-plugin.php` مفقود
- خطأ في صيغة PHP

**الحل:**
```bash
# تأكد من المسار الصحيح:
/wp-content/plugins/custom-review-plugin/custom-review-plugin.php

# تحقق من وجود جميع الملفات:
custom-review-plugin/
├── custom-review-plugin.php ✓
├── includes/ ✓
├── assets/ ✓
└── languages/ ✓
```

### 3. خطأ في قاعدة البيانات عند التفعيل

**الرسالة:** "Error creating database tables"

**الحل:**
1. تحقق من صلاحيات قاعدة البيانات
2. استخدم الإصلاح التلقائي في لوحة التحكم
3. أو قم بتشغيل هذا الكود:

```php
// أضف هذا مؤقتاً في functions.php للموضوع
function fix_custom_review_tables() {
    require_once WP_PLUGIN_DIR . '/custom-review-plugin/includes/class-database.php';
    $database = new CustomReview_Database();
    $database->create_tables();
    echo "تم إنشاء الجداول!";
}
// استدعي الدالة مرة واحدة ثم احذف الكود
```

### 4. الشورت كود لا يعمل

**المشكلة:** `[custom_reviews]` لا يظهر شيء

**الحل:**
1. تأكد من تفعيل الإضافة
2. تحقق من وجود أخطاء JavaScript في المتصفح
3. جرب الشورت كود البسيط أولاً:
```
[custom_review_stats]
```

### 5. النصوص تظهر بالإنجليزية

**السبب:** ملفات الترجمة لم يتم تحميلها

**الحل:**
```php
// في wp-config.php
define('WPLANG', 'ar');

// أو في لوحة التحكم:
// الإعدادات → عام → لغة الموقع → العربية
```

### 6. التصميم لا يظهر بشكل صحيح

**الأسباب:**
- تعارض مع موضوع الموقع
- ملفات CSS لم يتم تحميلها
- مشاكل في RTL

**الحل:**
1. فعّل "استخدام تصميم القالب" في الإعدادات
2. أو أضف هذا CSS مخصص:
```css
.custom-reviews-container {
    direction: rtl !important;
    text-align: right !important;
}
```

### 7. النموذج لا يرسل المراجعات

**الأسباب المحتملة:**
- المستخدم غير مسجل دخول
- خطأ في AJAX
- مشكلة في nonce

**الحل:**
1. تأكد من تسجيل دخول المستخدم
2. فحص وحدة تحكم المتصفح للأخطاء
3. تحقق من إعدادات AJAX في WordPress

### 8. المراجعات لا تظهر

**الأسباب:**
- المراجعات في حالة "قيد الانتظار"
- لا توجد مراجعات مقبولة
- خطأ في استعلام قاعدة البيانات

**الحل:**
1. اذهب إلى لوحة التحكم → المراجعات
2. اقبل المراجعات المعلقة
3. أو فعّل "قبول تلقائي" في الإعدادات

## 🔧 أدوات التشخيص

### فحص حالة الإضافة
أضف هذا الكود مؤقتاً لفحص حالة الإضافة:

```php
function custom_review_debug_info() {
    if (!current_user_can('manage_options')) return;
    
    echo "<div style='background: #f1f1f1; padding: 20px; margin: 20px;'>";
    echo "<h3>معلومات تشخيص إضافة المراجعات</h3>";
    
    // فحص الجداول
    global $wpdb;
    $table = $wpdb->prefix . 'custom_reviews';
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
    echo "<p>جدول قاعدة البيانات: " . ($exists ? "✅ موجود" : "❌ غير موجود") . "</p>";
    
    // فحص الإعدادات
    $require_login = get_option('custom_review_require_login', 'غير محدد');
    $auto_approve = get_option('custom_review_auto_approve', 'غير محدد');
    echo "<p>يتطلب تسجيل الدخول: $require_login</p>";
    echo "<p>قبول تلقائي: $auto_approve</p>";
    
    // فحص عدد المراجعات
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        echo "<p>عدد المراجعات: $count</p>";
    }
    
    echo "</div>";
}
add_action('wp_footer', 'custom_review_debug_info');
```

### فحص ملفات الإضافة
```bash
# تحقق من وجود جميع الملفات المطلوبة
ls -la wp-content/plugins/custom-review-plugin/

# يجب أن ترى:
# custom-review-plugin.php
# includes/
# assets/
# languages/
```

### فحص سجلات الأخطاء
```php
// في wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// ثم فحص الملف:
// wp-content/debug.log
```

## 📞 الحصول على المساعدة

### معلومات مطلوبة عند طلب المساعدة:
1. **إصدار WordPress:** (مثال: 6.4.2)
2. **إصدار PHP:** (مثال: 8.1)
3. **رسالة الخطأ الكاملة**
4. **خطوات إعادة إنتاج المشكلة**
5. **لقطة شاشة من المشكلة**

### فحص التوافق:
- WordPress 5.0+ ✅
- PHP 7.4+ ✅
- MySQL 5.6+ ✅
- jQuery مفعل ✅

## 🛠️ إصلاحات سريعة

### إعادة تعيين الإضافة
```sql
-- حذف جداول الإضافة (احذر!)
DROP TABLE IF EXISTS wp_custom_reviews;

-- حذف إعدادات الإضافة
DELETE FROM wp_options WHERE option_name LIKE 'custom_review_%';
```

### إعادة إنشاء الجداول
```php
// في functions.php مؤقتاً
function recreate_custom_review_tables() {
    require_once WP_PLUGIN_DIR . '/custom-review-plugin/includes/class-database.php';
    $db = new CustomReview_Database();
    $db->create_tables();
}
add_action('init', 'recreate_custom_review_tables');
```

### تنظيف البيانات
```sql
-- حذف المراجعات المرفوضة
DELETE FROM wp_custom_reviews WHERE status = 'rejected';

-- إعادة تعيين حالة جميع المراجعات
UPDATE wp_custom_reviews SET status = 'approved';
```

## ⚠️ تحذيرات مهمة

1. **عمل نسخة احتياطية** قبل أي تعديل
2. **لا تحذف الجداول** إلا إذا كنت متأكداً
3. **اختبر التغييرات** على موقع تجريبي أولاً
4. **احتفظ بنسخة** من ملفات الإضافة

## 📋 قائمة فحص سريعة

- [ ] اسم المجلد صحيح: `custom-review-plugin`
- [ ] جميع الملفات موجودة
- [ ] الإضافة مفعلة
- [ ] جدول قاعدة البيانات موجود
- [ ] اللغة العربية مفعلة
- [ ] المستخدم مسجل دخول (للاختبار)
- [ ] لا توجد أخطاء JavaScript
- [ ] الشورت كود في المكان الصحيح
