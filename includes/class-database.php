<?php
/**
 * Database operations for Custom Review Plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class CustomReview_Database {
    
    private $table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'custom_reviews';
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$this->table_name} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            rating tinyint(1) NOT NULL,
            comment text NOT NULL,
            status varchar(20) DEFAULT 'pending',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY status (status),
            <PERSON><PERSON><PERSON> created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Insert a new review
     */
    public function insert_review($user_id, $rating, $comment) {
        global $wpdb;
        
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'user_id' => $user_id,
                'rating' => $rating,
                'comment' => sanitize_textarea_field($comment),
                'status' => get_option('custom_review_auto_approve', 0) ? 'approved' : 'pending'
            ),
            array('%d', '%d', '%s', '%s')
        );
        
        return $result !== false ? $wpdb->insert_id : false;
    }
    
    /**
     * Get reviews with pagination
     */
    public function get_reviews($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'status' => 'approved',
            'limit' => 10,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $where = '';
        if (!empty($args['status'])) {
            $where = $wpdb->prepare("WHERE status = %s", $args['status']);
        }
        
        $sql = "SELECT r.*, u.display_name, u.user_email 
                FROM {$this->table_name} r 
                LEFT JOIN {$wpdb->users} u ON r.user_id = u.ID 
                {$where} 
                ORDER BY {$args['orderby']} {$args['order']} 
                LIMIT %d OFFSET %d";
        
        return $wpdb->get_results(
            $wpdb->prepare($sql, $args['limit'], $args['offset'])
        );
    }
    
    /**
     * Get review count
     */
    public function get_review_count($status = 'approved') {
        global $wpdb;
        
        if (empty($status)) {
            return $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
        }
        
        return $wpdb->get_var(
            $wpdb->prepare("SELECT COUNT(*) FROM {$this->table_name} WHERE status = %s", $status)
        );
    }
    
    /**
     * Get average rating
     */
    public function get_average_rating() {
        global $wpdb;
        
        $result = $wpdb->get_var(
            "SELECT AVG(rating) FROM {$this->table_name} WHERE status = 'approved'"
        );
        
        return $result ? round($result, 1) : 0;
    }
    
    /**
     * Get rating distribution
     */
    public function get_rating_distribution() {
        global $wpdb;
        
        $results = $wpdb->get_results(
            "SELECT rating, COUNT(*) as count 
             FROM {$this->table_name} 
             WHERE status = 'approved' 
             GROUP BY rating 
             ORDER BY rating DESC"
        );
        
        $distribution = array();
        for ($i = 5; $i >= 1; $i--) {
            $distribution[$i] = 0;
        }
        
        foreach ($results as $result) {
            $distribution[$result->rating] = $result->count;
        }
        
        return $distribution;
    }
    
    /**
     * Update review status
     */
    public function update_review_status($review_id, $status) {
        global $wpdb;
        
        return $wpdb->update(
            $this->table_name,
            array('status' => $status),
            array('id' => $review_id),
            array('%s'),
            array('%d')
        );
    }
    
    /**
     * Delete review
     */
    public function delete_review($review_id) {
        global $wpdb;
        
        return $wpdb->delete(
            $this->table_name,
            array('id' => $review_id),
            array('%d')
        );
    }
    
    /**
     * Check if user has already reviewed
     */
    public function user_has_reviewed($user_id) {
        global $wpdb;
        
        $count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE user_id = %d",
                $user_id
            )
        );
        
        return $count > 0;
    }
}
