<?php
/**
 * Helper functions for Custom Review Plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get reviews for display
 */
function custom_review_get_reviews($args = array()) {
    $database = new CustomReview_Database();
    return $database->get_reviews($args);
}

/**
 * Get review count
 */
function custom_review_get_count($status = 'approved') {
    $database = new CustomReview_Database();
    return $database->get_review_count($status);
}

/**
 * Get average rating
 */
function custom_review_get_average() {
    $database = new CustomReview_Database();
    return $database->get_average_rating();
}

/**
 * Check if user can submit review
 */
function custom_review_can_user_submit($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    $database = new CustomReview_Database();
    return !$database->user_has_reviewed($user_id);
}

/**
 * Render star rating HTML
 */
function custom_review_render_stars($rating, $max_stars = 5) {
    $output = '<div class="custom-review-stars">';
    
    for ($i = 1; $i <= $max_stars; $i++) {
        if ($i <= $rating) {
            $output .= '<span class="star filled">★</span>';
        } elseif ($i - 0.5 <= $rating) {
            $output .= '<span class="star half">★</span>';
        } else {
            $output .= '<span class="star empty">☆</span>';
        }
    }
    
    $output .= '</div>';
    return $output;
}

/**
 * Get review status label
 */
function custom_review_get_status_label($status) {
    $labels = array(
        'pending' => __('قيد الانتظار', 'custom-review'),
        'approved' => __('مقبولة', 'custom-review'),
        'rejected' => __('مرفوضة', 'custom-review')
    );
    
    return isset($labels[$status]) ? $labels[$status] : $status;
}

/**
 * Format review date
 */
function custom_review_format_date($date) {
    return date_i18n(get_option('date_format'), strtotime($date));
}

/**
 * Sanitize review comment
 */
function custom_review_sanitize_comment($comment) {
    return wp_kses_post(nl2br(sanitize_textarea_field($comment)));
}

/**
 * Check if reviews are enabled
 */
function custom_review_is_enabled() {
    return true; // Can be extended with settings
}

/**
 * Get plugin settings
 */
function custom_review_get_settings() {
    return array(
        'require_login' => get_option('custom_review_require_login', 1),
        'auto_approve' => get_option('custom_review_auto_approve', 0),
        'per_page' => get_option('custom_review_per_page', 10)
    );
}

/**
 * Log review activity (for future use)
 */
function custom_review_log_activity($action, $review_id, $user_id = null) {
    // This can be extended to log activities
    // For now, it's a placeholder for future logging functionality
    return true;
}
