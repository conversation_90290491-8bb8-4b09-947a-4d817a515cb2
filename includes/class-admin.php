<?php
/**
 * Admin interface for Custom Review Plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class CustomReview_Admin {
    
    private $database;
    
    public function __construct() {
        $this->database = new CustomReview_Database();
        $this->init_admin_hooks();
    }
    
    /**
     * Initialize admin hooks
     */
    private function init_admin_hooks() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('المراجعات المخصصة', 'custom-review'),
            __('المراجعات', 'custom-review'),
            'manage_options',
            'custom-reviews',
            array($this, 'admin_page'),
            'dashicons-star-filled',
            30
        );
        
        add_submenu_page(
            'custom-reviews',
            __('جميع المراجعات', 'custom-review'),
            __('جميع المراجعات', 'custom-review'),
            'manage_options',
            'custom-reviews',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'custom-reviews',
            __('الإعدادات', 'custom-review'),
            __('الإعدادات', 'custom-review'),
            'manage_options',
            'custom-reviews-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('custom_review_settings', 'custom_review_require_login');
        register_setting('custom_review_settings', 'custom_review_auto_approve');
        register_setting('custom_review_settings', 'custom_review_per_page');
        register_setting('custom_review_settings', 'custom_review_use_theme_styles');
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        $action = isset($_GET['action']) ? $_GET['action'] : 'list';
        
        switch ($action) {
            case 'approve':
                $this->handle_approve_review();
                break;
            case 'reject':
                $this->handle_reject_review();
                break;
            case 'delete':
                $this->handle_delete_review();
                break;
            default:
                $this->display_reviews_list();
                break;
        }
    }
    
    /**
     * Display reviews list
     */
    private function display_reviews_list() {
        $status = isset($_GET['status']) ? $_GET['status'] : '';
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
        
        $reviews = $this->database->get_reviews(array(
            'status' => $status,
            'limit' => $per_page,
            'offset' => $offset
        ));
        
        $total_reviews = $this->database->get_review_count($status);
        $total_pages = ceil($total_reviews / $per_page);
        
        ?>
        <div class="wrap">
            <h1><?php _e('المراجعات المخصصة', 'custom-review'); ?></h1>
            
            <ul class="subsubsub">
                <li><a href="?page=custom-reviews" <?php echo empty($status) ? 'class="current"' : ''; ?>><?php _e('الكل', 'custom-review'); ?></a> |</li>
                <li><a href="?page=custom-reviews&status=pending" <?php echo $status === 'pending' ? 'class="current"' : ''; ?>><?php _e('قيد الانتظار', 'custom-review'); ?></a> |</li>
                <li><a href="?page=custom-reviews&status=approved" <?php echo $status === 'approved' ? 'class="current"' : ''; ?>><?php _e('مقبولة', 'custom-review'); ?></a> |</li>
                <li><a href="?page=custom-reviews&status=rejected" <?php echo $status === 'rejected' ? 'class="current"' : ''; ?>><?php _e('مرفوضة', 'custom-review'); ?></a></li>
            </ul>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('المراجع', 'custom-review'); ?></th>
                        <th><?php _e('التقييم', 'custom-review'); ?></th>
                        <th><?php _e('التعليق', 'custom-review'); ?></th>
                        <th><?php _e('الحالة', 'custom-review'); ?></th>
                        <th><?php _e('التاريخ', 'custom-review'); ?></th>
                        <th><?php _e('الإجراءات', 'custom-review'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($reviews)): ?>
                        <tr>
                            <td colspan="6"><?php _e('لا توجد مراجعات', 'custom-review'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($reviews as $review): ?>
                            <tr>
                                <td><?php echo esc_html($review->display_name); ?></td>
                                <td>
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <span class="<?php echo $i <= $review->rating ? 'star-filled' : 'star-empty'; ?>">★</span>
                                    <?php endfor; ?>
                                    (<?php echo $review->rating; ?>)
                                </td>
                                <td><?php echo esc_html(wp_trim_words($review->comment, 10)); ?></td>
                                <td>
                                    <span class="status-<?php echo esc_attr($review->status); ?>">
                                        <?php 
                                        switch ($review->status) {
                                            case 'pending':
                                                _e('قيد الانتظار', 'custom-review');
                                                break;
                                            case 'approved':
                                                _e('مقبولة', 'custom-review');
                                                break;
                                            case 'rejected':
                                                _e('مرفوضة', 'custom-review');
                                                break;
                                        }
                                        ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($review->created_at))); ?></td>
                                <td>
                                    <?php if ($review->status === 'pending'): ?>
                                        <a href="?page=custom-reviews&action=approve&id=<?php echo $review->id; ?>" class="button button-primary"><?php _e('قبول', 'custom-review'); ?></a>
                                        <a href="?page=custom-reviews&action=reject&id=<?php echo $review->id; ?>" class="button"><?php _e('رفض', 'custom-review'); ?></a>
                                    <?php endif; ?>
                                    <a href="?page=custom-reviews&action=delete&id=<?php echo $review->id; ?>" class="button button-link-delete" onclick="return confirm('<?php esc_attr_e('هل أنت متأكد من حذف هذه المراجعة؟', 'custom-review'); ?>')"><?php _e('حذف', 'custom-review'); ?></a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <?php if ($total_pages > 1): ?>
                <div class="tablenav">
                    <div class="tablenav-pages">
                        <?php
                        echo paginate_links(array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'prev_text' => __('&laquo;'),
                            'next_text' => __('&raquo;'),
                            'total' => $total_pages,
                            'current' => $page
                        ));
                        ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        if (isset($_POST['submit'])) {
            update_option('custom_review_require_login', isset($_POST['custom_review_require_login']) ? 1 : 0);
            update_option('custom_review_auto_approve', isset($_POST['custom_review_auto_approve']) ? 1 : 0);
            update_option('custom_review_use_theme_styles', isset($_POST['custom_review_use_theme_styles']) ? 1 : 0);
            echo '<div class="notice notice-success"><p>' . __('تم حفظ الإعدادات', 'custom-review') . '</p></div>';
        }

        $require_login = get_option('custom_review_require_login', 1);
        $auto_approve = get_option('custom_review_auto_approve', 0);
        $use_theme_styles = get_option('custom_review_use_theme_styles', 0);
        
        ?>
        <div class="wrap">
            <h1><?php _e('إعدادات المراجعات', 'custom-review'); ?></h1>
            
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('يتطلب تسجيل الدخول', 'custom-review'); ?></th>
                        <td>
                            <input type="checkbox" name="custom_review_require_login" value="1" <?php checked($require_login, 1); ?> />
                            <p class="description"><?php _e('يجب على المستخدمين تسجيل الدخول لإضافة مراجعة', 'custom-review'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('قبول تلقائي', 'custom-review'); ?></th>
                        <td>
                            <input type="checkbox" name="custom_review_auto_approve" value="1" <?php checked($auto_approve, 1); ?> />
                            <p class="description"><?php _e('قبول المراجعات تلقائياً دون مراجعة إدارية', 'custom-review'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('استخدام تصميم القالب', 'custom-review'); ?></th>
                        <td>
                            <input type="checkbox" name="custom_review_use_theme_styles" value="1" <?php checked($use_theme_styles, 1); ?> />
                            <p class="description"><?php _e('استخدام تصميم وألوان القالب بدلاً من التصميم المخصص للإضافة', 'custom-review'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(__('حفظ الإعدادات', 'custom-review')); ?>
            </form>
            
            <h2><?php _e('الأكواد المختصرة المتاحة', 'custom-review'); ?></h2>
            <table class="widefat">
                <thead>
                    <tr>
                        <th><?php _e('الكود المختصر', 'custom-review'); ?></th>
                        <th><?php _e('الوصف', 'custom-review'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>[custom_reviews]</code></td>
                        <td><?php _e('عرض قائمة المراجعات مع النموذج والإحصائيات', 'custom-review'); ?></td>
                    </tr>
                    <tr>
                        <td><code>[custom_review_form]</code></td>
                        <td><?php _e('عرض نموذج إضافة مراجعة فقط', 'custom-review'); ?></td>
                    </tr>
                    <tr>
                        <td><code>[custom_review_stats]</code></td>
                        <td><?php _e('عرض إحصائيات المراجعات', 'custom-review'); ?></td>
                    </tr>
                    <tr>
                        <td><code>[custom_review_average]</code></td>
                        <td><?php _e('عرض متوسط التقييم فقط', 'custom-review'); ?></td>
                    </tr>
                    <tr>
                        <td><code>[custom_review_count]</code></td>
                        <td><?php _e('عرض عدد المراجعات فقط', 'custom-review'); ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <?php
    }
    
    /**
     * Handle approve review
     */
    private function handle_approve_review() {
        $review_id = intval($_GET['id']);
        if ($this->database->update_review_status($review_id, 'approved')) {
            wp_redirect(add_query_arg('message', 'approved', remove_query_arg(array('action', 'id'))));
        }
    }
    
    /**
     * Handle reject review
     */
    private function handle_reject_review() {
        $review_id = intval($_GET['id']);
        if ($this->database->update_review_status($review_id, 'rejected')) {
            wp_redirect(add_query_arg('message', 'rejected', remove_query_arg(array('action', 'id'))));
        }
    }
    
    /**
     * Handle delete review
     */
    private function handle_delete_review() {
        $review_id = intval($_GET['id']);
        if ($this->database->delete_review($review_id)) {
            wp_redirect(add_query_arg('message', 'deleted', remove_query_arg(array('action', 'id'))));
        }
    }
}
