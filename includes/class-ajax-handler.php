<?php
/**
 * AJAX Handler for Custom Review Plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class CustomReview_Ajax_Handler {
    
    private $database;
    
    public function __construct() {
        $this->database = new CustomReview_Database();
        $this->init_ajax_hooks();
    }
    
    /**
     * Initialize AJAX hooks
     */
    private function init_ajax_hooks() {
        // For logged-in users
        add_action('wp_ajax_submit_custom_review', array($this, 'handle_review_submission'));
        
        // For non-logged-in users (will redirect to login)
        add_action('wp_ajax_nopriv_submit_custom_review', array($this, 'handle_guest_review_submission'));
        
        // Admin actions
        add_action('wp_ajax_approve_review', array($this, 'approve_review'));
        add_action('wp_ajax_reject_review', array($this, 'reject_review'));
        add_action('wp_ajax_delete_review', array($this, 'delete_review'));
    }
    
    /**
     * Handle review submission for logged-in users
     */
    public function handle_review_submission() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'custom_review_nonce')) {
            wp_die(__('خطأ في التحقق من الأمان', 'custom-review'));
        }
        
        // Check if user is logged in
        if (!is_user_logged_in()) {
            wp_send_json_error(array(
                'message' => __('يجب تسجيل الدخول لإضافة مراجعة', 'custom-review')
            ));
        }
        
        $current_user = wp_get_current_user();
        
        // Check if user already reviewed
        if ($this->database->user_has_reviewed($current_user->ID)) {
            wp_send_json_error(array(
                'message' => __('لقد قمت بإضافة مراجعة من قبل', 'custom-review')
            ));
        }
        
        // Validate input
        $rating = intval($_POST['rating']);
        $comment = sanitize_textarea_field($_POST['comment']);
        
        if ($rating < 1 || $rating > 5) {
            wp_send_json_error(array(
                'message' => __('يرجى اختيار تقييم صحيح', 'custom-review')
            ));
        }
        
        if (empty($comment)) {
            wp_send_json_error(array(
                'message' => __('يرجى كتابة تعليق', 'custom-review')
            ));
        }
        
        // Insert review
        $review_id = $this->database->insert_review($current_user->ID, $rating, $comment);
        
        if ($review_id) {
            $auto_approve = get_option('custom_review_auto_approve', 0);
            $message = $auto_approve ? 
                __('تم إرسال المراجعة ونشرها بنجاح', 'custom-review') : 
                __('تم إرسال المراجعة بنجاح وهي قيد المراجعة', 'custom-review');
            
            wp_send_json_success(array(
                'message' => $message,
                'review_id' => $review_id
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('حدث خطأ أثناء حفظ المراجعة', 'custom-review')
            ));
        }
    }
    
    /**
     * Handle review submission for non-logged-in users
     */
    public function handle_guest_review_submission() {
        wp_send_json_error(array(
            'message' => __('يجب تسجيل الدخول لإضافة مراجعة', 'custom-review'),
            'login_required' => true,
            'login_url' => wp_login_url(get_permalink())
        ));
    }
    
    /**
     * Approve review (Admin only)
     */
    public function approve_review() {
        if (!current_user_can('manage_options')) {
            wp_die(__('ليس لديك صلاحية لتنفيذ هذا الإجراء', 'custom-review'));
        }
        
        $review_id = intval($_POST['review_id']);
        
        if ($this->database->update_review_status($review_id, 'approved')) {
            wp_send_json_success(array(
                'message' => __('تم قبول المراجعة', 'custom-review')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('حدث خطأ أثناء قبول المراجعة', 'custom-review')
            ));
        }
    }
    
    /**
     * Reject review (Admin only)
     */
    public function reject_review() {
        if (!current_user_can('manage_options')) {
            wp_die(__('ليس لديك صلاحية لتنفيذ هذا الإجراء', 'custom-review'));
        }
        
        $review_id = intval($_POST['review_id']);
        
        if ($this->database->update_review_status($review_id, 'rejected')) {
            wp_send_json_success(array(
                'message' => __('تم رفض المراجعة', 'custom-review')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('حدث خطأ أثناء رفض المراجعة', 'custom-review')
            ));
        }
    }
    
    /**
     * Delete review (Admin only)
     */
    public function delete_review() {
        if (!current_user_can('manage_options')) {
            wp_die(__('ليس لديك صلاحية لتنفيذ هذا الإجراء', 'custom-review'));
        }
        
        $review_id = intval($_POST['review_id']);
        
        if ($this->database->delete_review($review_id)) {
            wp_send_json_success(array(
                'message' => __('تم حذف المراجعة', 'custom-review')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('حدث خطأ أثناء حذف المراجعة', 'custom-review')
            ));
        }
    }
}
