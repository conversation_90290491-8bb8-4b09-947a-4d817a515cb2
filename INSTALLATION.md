# دليل التثبيت - إضافة المراجعات المخصصة

## متطلبات النظام

- WordPress 5.0 أو أحدث
- PHP 7.4 أو أحدث  
- MySQL 5.6 أو أحدث
- متصفح حديث مع دعم JavaScript

## خطوات التثبيت

### الطريقة الأولى: التثبيت اليدوي

1. **تحضير الملفات**
   ```bash
   # ضغط مجلد الإضافة
   zip -r custom-review-plugin.zip custom-review-plugin/
   ```

2. **رفع الإضافة**
   - اذهب إلى لوحة تحكم WordPress
   - الإضافات → أضف جديد
   - رفع إضافة → اختر الملف
   - ارفع ملف `custom-review-plugin.zip`

3. **تفعيل الإضافة**
   - اذهب إلى الإضافات → الإضافات المثبتة
   - ابحث عن "Custom Review Plugin"
   - اضغط "تفعيل"

### الطريقة الثانية: الرفع المباشر

1. **رفع الملفات عبر FTP**
   ```bash
   # ارفع المجلد كاملاً إلى
   /wp-content/plugins/custom-review-plugin/
   ```

2. **تعيين الصلاحيات**
   ```bash
   chmod 755 /wp-content/plugins/custom-review-plugin/
   chmod 644 /wp-content/plugins/custom-review-plugin/*.php
   ```

3. **تفعيل الإضافة من لوحة التحكم**

## التحقق من التثبيت

### 1. فحص قاعدة البيانات
بعد التفعيل، تأكد من إنشاء الجدول:
```sql
SHOW TABLES LIKE 'wp_custom_reviews';
```

### 2. فحص الملفات
تأكد من وجود الملفات التالية:
```
custom-review-plugin/
├── custom-review-plugin.php
├── includes/
│   ├── class-database.php
│   ├── class-shortcodes.php
│   ├── class-ajax-handler.php
│   ├── class-admin.php
│   └── functions.php
├── assets/
│   ├── css/
│   │   ├── frontend.css
│   │   └── admin.css
│   └── js/
│       ├── frontend.js
│       └── admin.js
└── languages/
    └── custom-review-ar.po
```

### 3. اختبار الوظائف الأساسية
- اذهب إلى WordPress Admin → المراجعات
- أضف الكود `[custom_reviews]` في أي صفحة
- تأكد من ظهور النموذج والإحصائيات

## الإعدادات الأولية

### 1. إعدادات الإضافة
- اذهب إلى المراجعات → الإعدادات
- فعّل "يتطلب تسجيل الدخول"
- اختر إعداد "قبول تلقائي" حسب احتياجك

### 2. إعدادات اللغة
```php
// في wp-config.php
define('WPLANG', 'ar');
```

### 3. إعدادات الموضوع (Theme)
أضف هذا الكود في `functions.php` للموضوع:
```php
// دعم RTL
function add_rtl_support() {
    if (is_rtl()) {
        wp_enqueue_style('rtl-style', get_template_directory_uri() . '/rtl.css');
    }
}
add_action('wp_enqueue_scripts', 'add_rtl_support');
```

## الاستخدام الأساسي

### 1. إضافة نظام المراجعات الكامل
```
[custom_reviews]
```

### 2. إضافة نموذج المراجعة فقط
```
[custom_review_form]
```

### 3. إضافة الإحصائيات فقط
```
[custom_review_stats]
```

## حل المشاكل الشائعة

### المشكلة: الإضافة لا تظهر في قائمة الإضافات
**الحل:**
1. تأكد من رفع الملفات في المجلد الصحيح
2. تحقق من صلاحيات الملفات
3. فحص ملف `custom-review-plugin.php` للأخطاء

### المشكلة: خطأ في قاعدة البيانات
**الحل:**
```php
// إضافة هذا الكود مؤقتاً في functions.php
function force_create_review_tables() {
    $database = new CustomReview_Database();
    $database->create_tables();
}
// ثم استدعاء الدالة مرة واحدة
```

### المشكلة: النصوص تظهر بالإنجليزية
**الحل:**
1. تأكد من تفعيل اللغة العربية في WordPress
2. تحقق من وجود ملف `custom-review-ar.po`
3. أعد تفعيل الإضافة

### المشكلة: النموذج لا يعمل
**الحل:**
1. تأكد من تحميل jQuery
2. فحص وحدة تحكم المتصفح للأخطاء
3. تحقق من إعدادات AJAX في WordPress

### المشكلة: التصميم لا يظهر بشكل صحيح
**الحل:**
1. تأكد من تحميل ملفات CSS
2. فحص تعارض مع موضوع الموقع
3. إضافة `!important` للتصميمات المخصصة

## الصيانة والتحديث

### نسخ احتياطي قبل التحديث
```sql
-- نسخ احتياطي لجدول المراجعات
CREATE TABLE wp_custom_reviews_backup AS SELECT * FROM wp_custom_reviews;
```

### تحديث الإضافة
1. عمل نسخة احتياطية من قاعدة البيانات
2. رفع الملفات الجديدة
3. إلغاء تفعيل وإعادة تفعيل الإضافة

### مراقبة الأداء
```php
// إضافة هذا الكود لمراقبة استعلامات قاعدة البيانات
define('SAVEQUERIES', true);
```

## الأمان

### حماية الملفات
أضف هذا في `.htaccess`:
```apache
# حماية ملفات الإضافة
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>
```

### تحديث كلمات المرور
غيّر كلمات مرور قاعدة البيانات بانتظام

### مراقبة المراجعات
راجع المراجعات المرسلة بانتظام لمنع الرسائل غير المرغوبة

## الدعم الفني

للحصول على المساعدة:
1. راجع ملف README.md
2. فحص سجلات الأخطاء في WordPress
3. تواصل مع مطور الإضافة

## معلومات إضافية

### متطلبات الخادم المُوصى بها
- ذاكرة PHP: 128MB أو أكثر
- وقت تنفيذ PHP: 30 ثانية أو أكثر
- مساحة قاعدة البيانات: 10MB للبداية

### التوافق مع الإضافات الأخرى
الإضافة متوافقة مع:
- WooCommerce
- Contact Form 7  
- Yoast SEO
- WPML (للمواقع متعددة اللغات)

### الأداء
- الإضافة محسّنة للسرعة
- تستخدم AJAX لتحسين تجربة المستخدم
- تدعم التخزين المؤقت
