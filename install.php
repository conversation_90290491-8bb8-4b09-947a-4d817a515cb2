<?php
/**
 * Installation Helper Script for Custom Review Plugin
 * 
 * This script helps fix common installation issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    die('Direct access not permitted.');
}

class CustomReviewInstaller {
    
    public function __construct() {
        add_action('admin_notices', array($this, 'show_installation_notice'));
        add_action('wp_ajax_fix_custom_review_installation', array($this, 'fix_installation'));
    }
    
    /**
     * Show installation notice if there are issues
     */
    public function show_installation_notice() {
        if (!current_user_can('activate_plugins')) {
            return;
        }
        
        // Check if plugin folder has spaces
        $plugin_dir = dirname(__FILE__);
        $plugin_name = basename($plugin_dir);
        
        if (strpos($plugin_name, ' ') !== false) {
            ?>
            <div class="notice notice-error">
                <h3>مشكلة في تثبيت إضافة المراجعات</h3>
                <p><strong>المشكلة:</strong> اسم مجلد الإضافة يحتوي على مسافات: <code><?php echo esc_html($plugin_name); ?></code></p>
                <p><strong>الحل:</strong> يجب إعادة تسمية المجلد إلى: <code>custom-review-plugin</code></p>
                <ol>
                    <li>إلغاء تفعيل الإضافة</li>
                    <li>إعادة تسمية المجلد من <code><?php echo esc_html($plugin_name); ?></code> إلى <code>custom-review-plugin</code></li>
                    <li>إعادة تفعيل الإضافة</li>
                </ol>
                <p><em>أو استخدم الزر أدناه للإصلاح التلقائي:</em></p>
                <button type="button" class="button button-primary" onclick="fixInstallation()">
                    إصلاح تلقائي
                </button>
            </div>
            <script>
            function fixInstallation() {
                if (confirm('هل تريد إصلاح مشكلة التثبيت تلقائياً؟')) {
                    jQuery.post(ajaxurl, {
                        action: 'fix_custom_review_installation',
                        nonce: '<?php echo wp_create_nonce('fix_installation'); ?>'
                    }, function(response) {
                        if (response.success) {
                            alert('تم الإصلاح بنجاح! يرجى إعادة تحميل الصفحة.');
                            location.reload();
                        } else {
                            alert('فشل الإصلاح: ' + response.data);
                        }
                    });
                }
            }
            </script>
            <?php
        }
        
        // Check if database tables exist
        global $wpdb;
        $table_name = $wpdb->prefix . 'custom_reviews';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        
        if (!$table_exists) {
            ?>
            <div class="notice notice-warning">
                <h3>تحذير: جدول قاعدة البيانات غير موجود</h3>
                <p>لم يتم إنشاء جدول المراجعات في قاعدة البيانات.</p>
                <button type="button" class="button button-primary" onclick="createTables()">
                    إنشاء الجداول الآن
                </button>
            </div>
            <script>
            function createTables() {
                jQuery.post(ajaxurl, {
                    action: 'create_custom_review_tables',
                    nonce: '<?php echo wp_create_nonce('create_tables'); ?>'
                }, function(response) {
                    if (response.success) {
                        alert('تم إنشاء الجداول بنجاح!');
                        location.reload();
                    } else {
                        alert('فشل إنشاء الجداول: ' + response.data);
                    }
                });
            }
            </script>
            <?php
        }
    }
    
    /**
     * Fix installation issues via AJAX
     */
    public function fix_installation() {
        if (!wp_verify_nonce($_POST['nonce'], 'fix_installation')) {
            wp_die('Security check failed');
        }
        
        if (!current_user_can('activate_plugins')) {
            wp_die('Insufficient permissions');
        }
        
        // Try to create database tables
        require_once dirname(__FILE__) . '/includes/class-database.php';
        $database = new CustomReview_Database();
        $result = $database->create_tables();
        
        if ($result) {
            wp_send_json_success('تم إصلاح المشكلة بنجاح');
        } else {
            wp_send_json_error('فشل في إنشاء جداول قاعدة البيانات');
        }
    }
}

// Initialize installer if we're in admin
if (is_admin()) {
    new CustomReviewInstaller();
}

// Add AJAX handler for table creation
add_action('wp_ajax_create_custom_review_tables', function() {
    if (!wp_verify_nonce($_POST['nonce'], 'create_tables')) {
        wp_die('Security check failed');
    }
    
    if (!current_user_can('activate_plugins')) {
        wp_die('Insufficient permissions');
    }
    
    require_once dirname(__FILE__) . '/includes/class-database.php';
    $database = new CustomReview_Database();
    $result = $database->create_tables();
    
    if ($result) {
        wp_send_json_success('تم إنشاء الجداول بنجاح');
    } else {
        wp_send_json_error('فشل في إنشاء الجداول');
    }
});
?>
