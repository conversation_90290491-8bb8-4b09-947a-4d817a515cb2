<?php
/**
 * Plugin Name: Custom Review Plugin
 * Plugin URI: https://yourwebsite.com/
 * Description: A custom WordPress plugin for managing product/service reviews with Arabic language support and user authentication.
 * Version: 1.0.0
 * Author: Your Name
 * Text Domain: custom-review
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('CUSTOM_REVIEW_VERSION', '1.0.0');
define('CUSTOM_REVIEW_PLUGIN_URL', plugin_dir_url(__FILE__));
define('CUSTOM_REVIEW_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('CUSTOM_REVIEW_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Custom Review Plugin Class
 */
class CustomReviewPlugin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Load required files
        $this->load_dependencies();
        
        // Initialize components
        $this->init_hooks();
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once CUSTOM_REVIEW_PLUGIN_PATH . 'includes/class-database.php';
        require_once CUSTOM_REVIEW_PLUGIN_PATH . 'includes/class-shortcodes.php';
        require_once CUSTOM_REVIEW_PLUGIN_PATH . 'includes/class-ajax-handler.php';
        require_once CUSTOM_REVIEW_PLUGIN_PATH . 'includes/class-admin.php';
        require_once CUSTOM_REVIEW_PLUGIN_PATH . 'includes/functions.php';
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Initialize components
        new CustomReview_Database();
        new CustomReview_Shortcodes();
        new CustomReview_Ajax_Handler();
        
        if (is_admin()) {
            new CustomReview_Admin();
        }
    }
    
    /**
     * Load plugin text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'custom-review',
            false,
            dirname(CUSTOM_REVIEW_PLUGIN_BASENAME) . '/languages/'
        );
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Check if theme styles should be used
        $use_theme_styles = get_option('custom_review_use_theme_styles', 0);

        if (!$use_theme_styles) {
            // Load full plugin styles
            wp_enqueue_style(
                'custom-review-style',
                CUSTOM_REVIEW_PLUGIN_URL . 'assets/css/frontend.css',
                array(),
                CUSTOM_REVIEW_VERSION
            );
        } else {
            // Load minimal styles for essential functionality only
            wp_add_inline_style('wp-block-library', '
                .custom-reviews-container { direction: rtl; text-align: right; }
                .star-rating { display: inline-block; }
                .star-rating input[type="radio"] { display: none; }
                .star-rating label { color: #ddd; cursor: pointer; font-size: 20px; }
                .star-rating label:hover, .star-rating label:hover ~ label,
                .star-rating input[type="radio"]:checked ~ label { color: #ffc107; }
                .stars-display .star.filled { color: #ffc107; }
                .stars-display .star.empty { color: #ddd; }
            ');
        }

        wp_enqueue_script(
            'custom-review-script',
            CUSTOM_REVIEW_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            CUSTOM_REVIEW_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('custom-review-script', 'customReview', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('custom_review_nonce'),
            'strings' => array(
                'login_required' => __('يجب تسجيل الدخول لإضافة مراجعة', 'custom-review'),
                'rating_required' => __('يرجى اختيار تقييم', 'custom-review'),
                'comment_required' => __('يرجى كتابة تعليق', 'custom-review'),
                'success_message' => __('تم إرسال المراجعة بنجاح', 'custom-review'),
                'error_message' => __('حدث خطأ، يرجى المحاولة مرة أخرى', 'custom-review')
            )
        ));
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        if (strpos($hook, 'custom-review') !== false) {
            wp_enqueue_style(
                'custom-review-admin-style',
                CUSTOM_REVIEW_PLUGIN_URL . 'assets/css/admin.css',
                array(),
                CUSTOM_REVIEW_VERSION
            );
            
            wp_enqueue_script(
                'custom-review-admin-script',
                CUSTOM_REVIEW_PLUGIN_URL . 'assets/js/admin.js',
                array('jquery'),
                CUSTOM_REVIEW_VERSION,
                true
            );
        }
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $database = new CustomReview_Database();
        $database->create_tables();
        
        // Set default options
        add_option('custom_review_version', CUSTOM_REVIEW_VERSION);
        add_option('custom_review_require_login', 1);
        add_option('custom_review_auto_approve', 0);
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
        // Note: We don't delete tables on deactivation, only on uninstall
    }
}

// Initialize the plugin
new CustomReviewPlugin();
