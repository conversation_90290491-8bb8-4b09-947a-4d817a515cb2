/* Custom Review Plugin - Frontend Styles */

/* RTL Support */
.custom-reviews-container {
  direction: rtl;
  text-align: right;
  font-family: inherit; /* Use theme's font */
}

/* Review Statistics */
.custom-review-stats {
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.stats-summary {
  text-align: center;
  margin-bottom: 20px;
}

.average-rating {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.rating-number {
  font-size: 2.5em;
  font-weight: bold;
  color: #333;
}

.rating-stars .star {
  font-size: 1.5em;
  color: #ffc107;
}

.rating-count {
  color: #666;
  font-size: 0.9em;
}

/* Rating Distribution */
.rating-distribution {
  margin-top: 20px;
}

.rating-bar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 10px;
}

.rating-label {
  min-width: 40px;
  font-size: 0.9em;
}

.bar-container {
  flex: 1;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: #ffc107;
  transition: width 0.3s ease;
}

.rating-bar .rating-count {
  min-width: 30px;
  text-align: center;
  font-size: 0.8em;
}

/* Review Form */
.custom-review-form-wrapper {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 30px;
}

.custom-review-form-wrapper h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #007cba;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.required {
  color: #d63638;
}

/* Star Rating Input */
.star-rating {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 5px;
  margin-bottom: 10px;
}

.star-rating input[type="radio"] {
  display: none;
}

.star-rating .star {
  font-size: 2em;
  color: #ddd;
  cursor: pointer;
  transition: color 0.2s ease;
}

.star-rating .star:hover,
.star-rating .star:hover ~ .star,
.star-rating input[type="radio"]:checked ~ .star {
  color: #ffc107;
}

/* Comment Textarea */
#review-comment {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  direction: rtl;
  text-align: right;
}

#review-comment:focus {
  outline: none;
  border-color: #007cba;
  box-shadow: 0 0 0 1px #007cba;
}

/* Login Notice */
.login-notice {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 15px;
}

.login-notice p {
  margin: 0;
  color: #856404;
}

/* Submit Button */
.submit-review-btn {
  background: #007cba;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-review-btn:hover {
  background: #005a87;
}

.submit-review-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Form Messages */
.form-messages {
  margin-top: 15px;
}

.form-messages .success {
  background: #d1edff;
  border: 1px solid #007cba;
  color: #004085;
  padding: 12px;
  border-radius: 4px;
}

.form-messages .error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
}

/* Already Reviewed Message */
.already-reviewed {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
  padding: 15px;
  border-radius: 4px;
  text-align: center;
}

/* Reviews List */
.custom-reviews-list h3 {
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #007cba;
  padding-bottom: 10px;
}

.no-reviews {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px 20px;
  background: #f9f9f9;
  border-radius: 8px;
}

/* Individual Review Item */
.custom-review-item {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  transition: box-shadow 0.2s ease;
}

.custom-review-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.reviewer-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.reviewer-name {
  font-weight: bold;
  color: #333;
  font-size: 1.1em;
}

.review-date {
  color: #666;
  font-size: 0.9em;
}

.review-rating {
  display: flex;
  align-items: center;
}

/* Stars Display */
.stars-display .star {
  font-size: 1.2em;
  margin-left: 2px;
}

.stars-display .star.filled {
  color: #ffc107;
}

.stars-display .star.half {
  color: #ffc107;
}

.stars-display .star.empty {
  color: #ddd;
}

/* Review Content */
.review-content {
  line-height: 1.6;
  color: #333;
}

.review-content p {
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-reviews-container {
    padding: 0 10px;
  }

  .custom-review-form-wrapper,
  .custom-review-stats {
    padding: 15px;
  }

  .review-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .star-rating .star {
    font-size: 1.5em;
  }

  .rating-number {
    font-size: 2em;
  }
}

/* Loading State */
.custom-review-loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.custom-review-loading:before {
  content: "⏳";
  margin-left: 10px;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.hidden {
  display: none;
}
