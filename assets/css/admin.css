/* Custom Review Plugin - Admin Styles */

/* Admin Page Styles */
.custom-reviews-admin {
    direction: rtl;
    text-align: right;
}

/* Status Indicators */
.status-pending {
    color: #d63638;
    font-weight: bold;
}

.status-approved {
    color: #00a32a;
    font-weight: bold;
}

.status-rejected {
    color: #646970;
    font-weight: bold;
}

/* Star Rating in Admin */
.star-filled {
    color: #ffc107;
}

.star-empty {
    color: #ddd;
}

/* Admin Table Enhancements */
.wp-list-table .column-rating {
    width: 120px;
}

.wp-list-table .column-status {
    width: 100px;
}

.wp-list-table .column-actions {
    width: 150px;
}

/* Review Content Preview */
.review-content-preview {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Action Buttons */
.review-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.review-actions .button {
    margin: 0;
}

/* Settings Page */
.custom-review-settings {
    max-width: 800px;
}

.custom-review-settings .form-table th {
    width: 200px;
}

/* Shortcode Reference Table */
.shortcode-reference {
    margin-top: 30px;
}

.shortcode-reference table {
    border-collapse: collapse;
}

.shortcode-reference th,
.shortcode-reference td {
    padding: 12px;
    border: 1px solid #ddd;
    text-align: right;
}

.shortcode-reference th {
    background: #f9f9f9;
    font-weight: bold;
}

.shortcode-reference code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    direction: ltr;
    display: inline-block;
}

/* Statistics Dashboard */
.review-stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-box {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-box h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-box .stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #007cba;
    margin: 10px 0;
}

.stat-box .stat-label {
    color: #666;
    font-size: 12px;
}

/* Filter Tabs */
.subsubsub {
    margin-bottom: 20px;
}

.subsubsub a.current {
    font-weight: bold;
    color: #000;
}

/* Responsive Admin */
@media (max-width: 768px) {
    .review-actions {
        flex-direction: column;
    }
    
    .review-stats-dashboard {
        grid-template-columns: 1fr;
    }
    
    .wp-list-table .column-comment {
        display: none;
    }
}

/* Loading States */
.admin-loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.admin-loading:before {
    content: "⏳";
    margin-left: 10px;
}

/* Success/Error Messages */
.notice.custom-review-notice {
    border-right: 4px solid #007cba;
}

.notice.custom-review-notice.notice-success {
    border-right-color: #00a32a;
}

.notice.custom-review-notice.notice-error {
    border-right-color: #d63638;
}

/* Bulk Actions */
.bulkactions select,
.bulkactions input {
    margin-left: 5px;
}

/* Review Details Modal (if implemented) */
.review-modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.review-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    direction: rtl;
    text-align: right;
}

.review-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.review-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.review-modal-close:hover {
    color: #000;
}

/* Pagination */
.tablenav-pages {
    text-align: left;
}

.tablenav-pages .page-numbers {
    direction: ltr;
}

/* Form Validation */
.form-invalid {
    border-color: #d63638 !important;
    box-shadow: 0 0 0 1px #d63638;
}

.validation-error {
    color: #d63638;
    font-size: 12px;
    margin-top: 5px;
}

/* Help Text */
.help-text {
    color: #666;
    font-style: italic;
    font-size: 13px;
    margin-top: 5px;
}

/* Toggle Switches (for settings) */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #007cba;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Print Styles */
@media print {
    .review-actions,
    .bulkactions,
    .tablenav {
        display: none;
    }
}
