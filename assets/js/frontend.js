/**
 * Custom Review Plugin - Frontend JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initCustomReviews();
    });

    /**
     * Initialize custom reviews functionality
     */
    function initCustomReviews() {
        initStarRating();
        initReviewForm();
    }

    /**
     * Initialize star rating functionality
     */
    function initStarRating() {
        $('.star-rating .star').on('mouseenter', function() {
            var rating = $(this).siblings('input').val();
            highlightStars($(this).closest('.star-rating'), rating);
        });

        $('.star-rating').on('mouseleave', function() {
            var checkedRating = $(this).find('input:checked').val() || 0;
            highlightStars($(this), checkedRating);
        });

        $('.star-rating input').on('change', function() {
            var rating = $(this).val();
            highlightStars($(this).closest('.star-rating'), rating);
        });
    }

    /**
     * Highlight stars based on rating
     */
    function highlightStars($container, rating) {
        $container.find('.star').each(function() {
            var starValue = $(this).siblings('input').val();
            if (starValue <= rating) {
                $(this).css('color', '#ffc107');
            } else {
                $(this).css('color', '#ddd');
            }
        });
    }

    /**
     * Initialize review form
     */
    function initReviewForm() {
        $('#custom-review-form').on('submit', function(e) {
            e.preventDefault();
            submitReview($(this));
        });
    }

    /**
     * Submit review via AJAX
     */
    function submitReview($form) {
        var $submitBtn = $form.find('.submit-review-btn');
        var $messages = $form.find('.form-messages');
        
        // Get form data
        var rating = $form.find('input[name="rating"]:checked').val();
        var comment = $form.find('#review-comment').val().trim();
        var nonce = $form.find('#custom_review_nonce').val();

        // Clear previous messages
        $messages.empty();

        // Validate form
        if (!rating) {
            showMessage($messages, customReview.strings.rating_required, 'error');
            return;
        }

        if (!comment) {
            showMessage($messages, customReview.strings.comment_required, 'error');
            return;
        }

        // Disable submit button
        $submitBtn.prop('disabled', true).text('جاري الإرسال...');

        // Prepare data
        var data = {
            action: 'submit_custom_review',
            rating: rating,
            comment: comment,
            nonce: nonce
        };

        // Submit via AJAX
        $.ajax({
            url: customReview.ajaxurl,
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showMessage($messages, response.data.message, 'success');
                    $form[0].reset();
                    
                    // Optionally reload reviews or hide form
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    if (response.data.login_required) {
                        showLoginPrompt(response.data.login_url);
                    } else {
                        showMessage($messages, response.data.message, 'error');
                    }
                }
            },
            error: function() {
                showMessage($messages, customReview.strings.error_message, 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text('إرسال المراجعة');
            }
        });
    }

    /**
     * Show message to user
     */
    function showMessage($container, message, type) {
        var $message = $('<div class="' + type + '">' + message + '</div>');
        $container.html($message);
        
        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(function() {
                $message.fadeOut();
            }, 5000);
        }
    }

    /**
     * Show login prompt
     */
    function showLoginPrompt(loginUrl) {
        var message = customReview.strings.login_required;
        
        if (confirm(message + '\n\nهل تريد الانتقال لصفحة تسجيل الدخول؟')) {
            window.location.href = loginUrl;
        }
    }

    /**
     * Format numbers for Arabic
     */
    function formatArabicNumber(number) {
        var arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return number.toString().replace(/[0-9]/g, function(w) {
            return arabicNumbers[+w];
        });
    }

    /**
     * Animate rating bars (if visible)
     */
    function animateRatingBars() {
        $('.rating-bar .bar-fill').each(function() {
            var $bar = $(this);
            var width = $bar.data('width') || $bar.css('width');
            
            $bar.css('width', '0%').animate({
                width: width
            }, 1000);
        });
    }

    // Animate bars when they come into view
    $(window).on('scroll', function() {
        $('.custom-review-stats').each(function() {
            if (isElementInViewport(this) && !$(this).hasClass('animated')) {
                $(this).addClass('animated');
                animateRatingBars();
            }
        });
    });

    /**
     * Check if element is in viewport
     */
    function isElementInViewport(el) {
        var rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Handle responsive behavior
     */
    function handleResponsive() {
        var $container = $('.custom-reviews-container');
        
        if ($(window).width() < 768) {
            $container.addClass('mobile-view');
        } else {
            $container.removeClass('mobile-view');
        }
    }

    // Handle window resize
    $(window).on('resize', handleResponsive);
    handleResponsive(); // Initial call

})(jQuery);
