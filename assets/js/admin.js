/**
 * Custom Review Plugin - Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initAdminReviews();
    });

    /**
     * Initialize admin functionality
     */
    function initAdminReviews() {
        initQuickActions();
        initBulkActions();
        initConfirmDialogs();
        initTooltips();
    }

    /**
     * Initialize quick action buttons
     */
    function initQuickActions() {
        $('.review-quick-approve').on('click', function(e) {
            e.preventDefault();
            var reviewId = $(this).data('review-id');
            var $row = $(this).closest('tr');
            
            quickAction('approve', reviewId, $row);
        });

        $('.review-quick-reject').on('click', function(e) {
            e.preventDefault();
            var reviewId = $(this).data('review-id');
            var $row = $(this).closest('tr');
            
            quickAction('reject', reviewId, $row);
        });

        $('.review-quick-delete').on('click', function(e) {
            e.preventDefault();
            
            if (confirm('هل أنت متأكد من حذف هذه المراجعة؟')) {
                var reviewId = $(this).data('review-id');
                var $row = $(this).closest('tr');
                
                quickAction('delete', reviewId, $row);
            }
        });
    }

    /**
     * Perform quick action via AJAX
     */
    function quickAction(action, reviewId, $row) {
        var $spinner = $('<span class="spinner is-active" style="float: none; margin: 0 5px;"></span>');
        $row.find('.review-actions').append($spinner);

        var data = {
            action: action + '_review',
            review_id: reviewId,
            _wpnonce: $('#_wpnonce').val()
        };

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showNotice(response.data.message, 'success');
                    
                    // Update row or remove it
                    if (action === 'delete') {
                        $row.fadeOut(300, function() {
                            $(this).remove();
                            updateRowCount();
                        });
                    } else {
                        updateRowStatus($row, action);
                    }
                } else {
                    showNotice(response.data.message, 'error');
                }
            },
            error: function() {
                showNotice('حدث خطأ أثناء تنفيذ العملية', 'error');
            },
            complete: function() {
                $spinner.remove();
            }
        });
    }

    /**
     * Update row status after action
     */
    function updateRowStatus($row, action) {
        var $statusCell = $row.find('.column-status');
        var $actionsCell = $row.find('.column-actions');
        
        if (action === 'approve') {
            $statusCell.html('<span class="status-approved">مقبولة</span>');
            $actionsCell.find('.review-quick-approve').remove();
        } else if (action === 'reject') {
            $statusCell.html('<span class="status-rejected">مرفوضة</span>');
            $actionsCell.find('.review-quick-approve, .review-quick-reject').remove();
        }
        
        $row.addClass('updated');
        setTimeout(function() {
            $row.removeClass('updated');
        }, 2000);
    }

    /**
     * Initialize bulk actions
     */
    function initBulkActions() {
        $('#doaction, #doaction2').on('click', function(e) {
            var action = $(this).siblings('select').val();
            var $checkedBoxes = $('input[name="review[]"]:checked');
            
            if (action === '-1' || $checkedBoxes.length === 0) {
                return;
            }
            
            e.preventDefault();
            
            var confirmMessage = '';
            switch (action) {
                case 'approve':
                    confirmMessage = 'هل تريد قبول المراجعات المحددة؟';
                    break;
                case 'reject':
                    confirmMessage = 'هل تريد رفض المراجعات المحددة؟';
                    break;
                case 'delete':
                    confirmMessage = 'هل أنت متأكد من حذف المراجعات المحددة؟';
                    break;
            }
            
            if (confirm(confirmMessage)) {
                performBulkAction(action, $checkedBoxes);
            }
        });
    }

    /**
     * Perform bulk action
     */
    function performBulkAction(action, $checkedBoxes) {
        var reviewIds = [];
        $checkedBoxes.each(function() {
            reviewIds.push($(this).val());
        });

        var $spinner = $('<span class="spinner is-active" style="float: none; margin: 0 10px;"></span>');
        $('.bulkactions').append($spinner);

        var data = {
            action: 'bulk_' + action + '_reviews',
            review_ids: reviewIds,
            _wpnonce: $('#_wpnonce').val()
        };

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showNotice(response.data.message, 'success');
                    location.reload();
                } else {
                    showNotice(response.data.message, 'error');
                }
            },
            error: function() {
                showNotice('حدث خطأ أثناء تنفيذ العملية', 'error');
            },
            complete: function() {
                $spinner.remove();
            }
        });
    }

    /**
     * Initialize confirmation dialogs
     */
    function initConfirmDialogs() {
        $('.button-link-delete').on('click', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                e.preventDefault();
            }
        });
    }

    /**
     * Initialize tooltips
     */
    function initTooltips() {
        $('[data-tooltip]').each(function() {
            $(this).attr('title', $(this).data('tooltip'));
        });
    }

    /**
     * Show admin notice
     */
    function showNotice(message, type) {
        var noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
        var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible custom-review-notice"><p>' + message + '</p></div>');
        
        $('.wrap h1').after($notice);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $notice.fadeOut();
        }, 5000);
        
        // Add dismiss functionality
        $notice.on('click', '.notice-dismiss', function() {
            $notice.fadeOut();
        });
    }

    /**
     * Update row count after deletion
     */
    function updateRowCount() {
        var $table = $('.wp-list-table tbody');
        var rowCount = $table.find('tr').length;
        
        if (rowCount === 0) {
            $table.html('<tr><td colspan="6">لا توجد مراجعات</td></tr>');
        }
    }

    /**
     * Handle settings form
     */
    $('#custom-review-settings-form').on('submit', function(e) {
        var $form = $(this);
        var $submitBtn = $form.find('input[type="submit"]');
        
        $submitBtn.prop('disabled', true).val('جاري الحفظ...');
        
        // Re-enable after form submission
        setTimeout(function() {
            $submitBtn.prop('disabled', false).val('حفظ الإعدادات');
        }, 2000);
    });

    /**
     * Copy shortcode to clipboard
     */
    $('.copy-shortcode').on('click', function(e) {
        e.preventDefault();
        
        var shortcode = $(this).data('shortcode');
        var $temp = $('<input>');
        $('body').append($temp);
        $temp.val(shortcode).select();
        document.execCommand('copy');
        $temp.remove();
        
        showNotice('تم نسخ الكود المختصر', 'success');
    });

    /**
     * Filter reviews by status
     */
    $('.subsubsub a').on('click', function(e) {
        var $link = $(this);
        var href = $link.attr('href');
        
        // Add loading state
        $link.append(' <span class="spinner is-active" style="float: none; margin: 0;"></span>');
    });

    /**
     * Auto-refresh pending reviews count
     */
    function updatePendingCount() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_pending_reviews_count'
            },
            success: function(response) {
                if (response.success && response.data.count > 0) {
                    var $pendingLink = $('.subsubsub a[href*="status=pending"]');
                    var currentText = $pendingLink.text();
                    var newText = currentText.replace(/\(\d+\)/, '(' + response.data.count + ')');
                    $pendingLink.text(newText);
                }
            }
        });
    }

    // Update pending count every 30 seconds
    setInterval(updatePendingCount, 30000);

})(jQuery);
