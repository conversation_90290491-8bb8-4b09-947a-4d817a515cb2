# Custom Review Plugin

A comprehensive WordPress plugin for managing product/service reviews with full Arabic language support and user authentication.

## Features

- ⭐ **Star Rating System** - 5-star rating with interactive UI
- 🔐 **User Authentication** - Requires login to submit reviews
- 🌐 **Arabic Language Support** - Full RTL support and Arabic translations
- 📱 **Responsive Design** - Works perfectly on mobile devices
- 🎨 **Customizable Display** - Multiple shortcodes for different layouts
- 👨‍💼 **Admin Management** - Complete admin interface for review moderation
- 📊 **Statistics Dashboard** - Review analytics and rating distribution
- 🚫 **Spam Protection** - Login requirement prevents spam submissions

## Installation

1. **Upload the Plugin**
   - Upload the entire `custom-review-plugin` folder to `/wp-content/plugins/`
   - Or zip the folder and upload via WordPress admin

2. **Activate the Plugin**
   - Go to WordPress Admin → Plugins
   - Find "Custom Review Plugin" and click "Activate"

3. **Database Setup**
   - The plugin automatically creates necessary database tables upon activation

## Available Shortcodes

### Main Review System
```
[custom_reviews]
```
Displays the complete review system including:
- Review submission form
- Statistics dashboard
- List of approved reviews

**Attributes:**
- `limit="10"` - Number of reviews to display
- `show_form="true"` - Show/hide the review form
- `show_stats="true"` - Show/hide statistics

### Individual Components

#### Review Form Only
```
[custom_review_form]
```
Displays only the review submission form.

**Attributes:**
- `title="أضف مراجعتك"` - Custom form title

#### Statistics Only
```
[custom_review_stats]
```
Shows review statistics including average rating and distribution.

#### Average Rating
```
[custom_review_average]
```
Displays just the average rating with stars.

#### Review Count
```
[custom_review_count]
```
Shows the total number of reviews.

## Usage Examples

### Basic Implementation
Add this shortcode to any page or post:
```
[custom_reviews]
```

### Custom Layout
Create a custom layout using individual components:
```
<div class="my-reviews-section">
    <h2>تقييمات العملاء</h2>
    [custom_review_stats]
    
    <div class="review-form-section">
        [custom_review_form title="شاركنا رأيك"]
    </div>
    
    <div class="reviews-list">
        [custom_reviews show_form="false" show_stats="false" limit="5"]
    </div>
</div>
```

### Sidebar Widget
For displaying quick stats in sidebar:
```
<div class="review-widget">
    <h3>تقييم المنتج</h3>
    [custom_review_average]
    [custom_review_count]
</div>
```

## Admin Features

### Review Management
- **Dashboard**: Access via WordPress Admin → المراجعات
- **Approve/Reject**: Moderate reviews before they appear
- **Delete**: Remove inappropriate reviews
- **Bulk Actions**: Handle multiple reviews at once

### Settings
Access settings via WordPress Admin → المراجعات → الإعدادات

**Available Settings:**
- **Require Login**: Force users to login before reviewing
- **Auto Approve**: Automatically approve reviews without moderation

### Review Status
- **Pending** (قيد الانتظار): Awaiting admin approval
- **Approved** (مقبولة): Visible to public
- **Rejected** (مرفوضة): Hidden from public

## Customization

### CSS Customization
The plugin includes comprehensive CSS classes for customization:

```css
/* Main container */
.custom-reviews-container { }

/* Review form */
.custom-review-form-wrapper { }
.star-rating { }
.submit-review-btn { }

/* Review display */
.custom-review-item { }
.reviewer-name { }
.review-rating { }
.review-content { }

/* Statistics */
.custom-review-stats { }
.average-rating { }
.rating-distribution { }
```

### JavaScript Hooks
The plugin provides JavaScript events for custom functionality:

```javascript
// Review submitted successfully
$(document).on('reviewSubmitted', function(event, data) {
    console.log('Review submitted:', data);
});

// Review form validation
$(document).on('reviewValidation', function(event, isValid) {
    console.log('Form valid:', isValid);
});
```

## Security Features

1. **Nonce Verification**: All forms use WordPress nonces
2. **User Authentication**: Only logged-in users can submit
3. **Data Sanitization**: All input is properly sanitized
4. **SQL Injection Protection**: Uses WordPress prepared statements
5. **XSS Prevention**: Output is properly escaped

## Database Schema

The plugin creates one main table: `wp_custom_reviews`

```sql
CREATE TABLE wp_custom_reviews (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    rating tinyint(1) NOT NULL,
    comment text NOT NULL,
    status varchar(20) DEFAULT 'pending',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);
```

## Troubleshooting

### Common Issues

**Reviews not appearing:**
- Check if reviews are approved in admin panel
- Verify shortcode is correctly placed
- Ensure plugin is activated

**Form not submitting:**
- Check if user is logged in
- Verify JavaScript is enabled
- Check browser console for errors

**Arabic text not displaying correctly:**
- Ensure your theme supports RTL
- Check if Arabic fonts are loaded
- Verify charset is UTF-8

### Debug Mode
Add this to wp-config.php for debugging:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher
- Modern browser with JavaScript enabled

## Support

For support and customization requests, please contact the plugin developer.

## License

This plugin is licensed under GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Complete review system with Arabic support
- Admin management interface
- Multiple shortcodes
- Responsive design
- User authentication
- Spam protection
